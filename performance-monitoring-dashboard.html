<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dynamic Chart Rendering Performance Monitor</title>
    <style>
        body {
            font-family: 'Amazon Ember', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            color: #333;
        }
        .monitor-container {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .monitor-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .monitor-card h3 {
            margin: 0 0 15px 0;
            color: #470CED;
            font-size: 18px;
            font-weight: 600;
        }
        .metric-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .metric-row:last-child {
            border-bottom: none;
        }
        .metric-label {
            font-weight: 500;
            color: #606F95;
        }
        .metric-value {
            font-weight: 700;
            color: #1B1D21;
        }
        .metric-value.good {
            color: #04AE2C;
        }
        .metric-value.warning {
            color: #ff9100;
        }
        .metric-value.error {
            color: #FF391F;
        }
        .chart-list {
            max-height: 300px;
            overflow-y: auto;
        }
        .chart-item {
            padding: 10px;
            margin: 5px 0;
            background: #f8f9fa;
            border-radius: 5px;
            border-left: 4px solid #470CED;
        }
        .chart-item.destroyed {
            border-left-color: #ff9100;
            background: #fff3e0;
        }
        .chart-item.loading {
            border-left-color: #2196F3;
            background: #e3f2fd;
        }
        .chart-name {
            font-weight: 600;
            margin-bottom: 5px;
        }
        .chart-stats {
            font-size: 12px;
            color: #666;
        }
        .controls {
            grid-column: 1 / -1;
            display: flex;
            gap: 10px;
            align-items: center;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
        }
        .btn-primary {
            background: #470CED;
            color: white;
        }
        .btn-secondary {
            background: #e9ecef;
            color: #495057;
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-indicator.active {
            background: #04AE2C;
        }
        .status-indicator.inactive {
            background: #ccc;
        }
        .memory-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .memory-fill {
            height: 100%;
            background: linear-gradient(90deg, #04AE2C 0%, #ff9100 70%, #FF391F 90%);
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="monitor-container">
        <div class="controls">
            <h1>Dynamic Chart Rendering Performance Monitor</h1>
            <div style="margin-left: auto; display: flex; gap: 10px;">
                <button class="btn btn-primary" onclick="refreshMetrics()">Refresh</button>
                <button class="btn btn-secondary" onclick="clearStates()">Clear Old States</button>
                <button class="btn btn-secondary" onclick="toggleAutoRefresh()">
                    <span id="auto-refresh-text">Enable Auto-Refresh</span>
                </button>
            </div>
        </div>

        <div class="monitor-card">
            <h3>📊 System Overview</h3>
            <div class="metric-row">
                <span class="metric-label">Total Components</span>
                <span class="metric-value" id="total-components">-</span>
            </div>
            <div class="metric-row">
                <span class="metric-label">Loaded Charts</span>
                <span class="metric-value" id="loaded-charts">-</span>
            </div>
            <div class="metric-row">
                <span class="metric-label">Loading Charts</span>
                <span class="metric-value" id="loading-charts">-</span>
            </div>
            <div class="metric-row">
                <span class="metric-label">Destroyed Charts</span>
                <span class="metric-value" id="destroyed-charts">-</span>
            </div>
            <div class="metric-row">
                <span class="metric-label">Saved States</span>
                <span class="metric-value" id="saved-states">-</span>
            </div>
        </div>

        <div class="monitor-card">
            <h3>💾 Memory Usage</h3>
            <div class="metric-row">
                <span class="metric-label">Estimated Memory</span>
                <span class="metric-value" id="memory-usage">-</span>
            </div>
            <div class="memory-bar">
                <div class="memory-fill" id="memory-fill" style="width: 0%"></div>
            </div>
            <div class="metric-row">
                <span class="metric-label">Memory Efficiency</span>
                <span class="metric-value" id="memory-efficiency">-</span>
            </div>
            <div class="metric-row">
                <span class="metric-label">Sidebar Protection</span>
                <span class="metric-value" id="sidebar-protection">
                    <span class="status-indicator inactive"></span>Inactive
                </span>
            </div>
        </div>

        <div class="monitor-card">
            <h3>📈 Performance Metrics</h3>
            <div class="metric-row">
                <span class="metric-label">Avg Render Time</span>
                <span class="metric-value" id="avg-render-time">-</span>
            </div>
            <div class="metric-row">
                <span class="metric-label">Total Renders</span>
                <span class="metric-value" id="total-renders">-</span>
            </div>
            <div class="metric-row">
                <span class="metric-label">Re-renders</span>
                <span class="metric-value" id="re-renders">-</span>
            </div>
            <div class="metric-row">
                <span class="metric-label">Performance Score</span>
                <span class="metric-value" id="performance-score">-</span>
            </div>
        </div>

        <div class="monitor-card">
            <h3>🔄 Active Charts</h3>
            <div class="chart-list" id="chart-list">
                <div style="text-align: center; color: #666; padding: 20px;">
                    No chart data available
                </div>
            </div>
        </div>
    </div>

    <script>
        let autoRefreshInterval = null;
        let isAutoRefresh = false;

        function refreshMetrics() {
            if (!window.ViewportLazyLoader) {
                console.warn('ViewportLazyLoader not available');
                return;
            }

            try {
                const metrics = window.ViewportLazyLoader.getPerformanceMetrics();
                updateSystemOverview(metrics);
                updateMemoryUsage(metrics);
                updatePerformanceMetrics(metrics);
                updateChartList(metrics);
                updateSidebarProtection();
            } catch (error) {
                console.error('Failed to refresh metrics:', error);
            }
        }

        function updateSystemOverview(metrics) {
            document.getElementById('total-components').textContent = metrics.totalComponents;
            document.getElementById('loaded-charts').textContent = metrics.loadedComponents;
            document.getElementById('loading-charts').textContent = metrics.loadingComponents;
            document.getElementById('destroyed-charts').textContent = metrics.destroyedCharts;
            document.getElementById('saved-states').textContent = metrics.savedStates;
        }

        function updateMemoryUsage(metrics) {
            const memoryKB = metrics.memoryUsage.estimatedKB;
            const memoryMB = metrics.memoryUsage.estimatedMB;
            
            let memoryText = memoryKB < 1024 ? `${memoryKB} KB` : `${memoryMB} MB`;
            document.getElementById('memory-usage').textContent = memoryText;

            // Calculate memory efficiency (lower is better)
            const efficiency = metrics.savedStates > 0 ? Math.round(memoryKB / metrics.savedStates) : 0;
            document.getElementById('memory-efficiency').textContent = `${efficiency} KB/state`;

            // Update memory bar (assuming 10MB is high usage)
            const maxMemoryKB = 10240; // 10MB
            const percentage = Math.min((memoryKB / maxMemoryKB) * 100, 100);
            document.getElementById('memory-fill').style.width = `${percentage}%`;
        }

        function updatePerformanceMetrics(metrics) {
            const renderCounts = metrics.renderCounts;
            let totalRenders = 0;
            let reRenders = 0;
            let totalRenderTime = 0;

            Object.values(renderCounts).forEach(chart => {
                totalRenders += chart.renderCount;
                if (chart.renderCount > 1) {
                    reRenders += chart.renderCount - 1;
                }
            });

            document.getElementById('total-renders').textContent = totalRenders;
            document.getElementById('re-renders').textContent = reRenders;

            // Calculate performance score (higher is better)
            const destroyedRatio = metrics.totalComponents > 0 ? metrics.destroyedCharts / metrics.totalComponents : 0;
            const memoryEfficiency = metrics.memoryUsage.estimatedKB / Math.max(metrics.savedStates, 1);
            const performanceScore = Math.max(0, 100 - (destroyedRatio * 30) - (memoryEfficiency * 0.1));
            
            const scoreElement = document.getElementById('performance-score');
            scoreElement.textContent = `${Math.round(performanceScore)}%`;
            scoreElement.className = 'metric-value ' + (performanceScore > 80 ? 'good' : performanceScore > 60 ? 'warning' : 'error');
        }

        function updateChartList(metrics) {
            const chartList = document.getElementById('chart-list');
            const renderCounts = metrics.renderCounts;

            if (Object.keys(renderCounts).length === 0) {
                chartList.innerHTML = '<div style="text-align: center; color: #666; padding: 20px;">No chart data available</div>';
                return;
            }

            let html = '';
            Object.entries(renderCounts).forEach(([id, data]) => {
                const isDestroyed = window.ViewportLazyLoader.destroyedCharts.has(id);
                const isLoading = window.ViewportLazyLoader.loadingComponents.has(id);
                
                let status = 'loaded';
                if (isLoading) status = 'loading';
                else if (isDestroyed) status = 'destroyed';

                html += `
                    <div class="chart-item ${status}">
                        <div class="chart-name">${id}</div>
                        <div class="chart-stats">
                            Renders: ${data.renderCount} | 
                            Dynamic: ${data.isDynamic ? 'Yes' : 'No'} | 
                            Status: ${status.charAt(0).toUpperCase() + status.slice(1)}
                            ${data.lastRenderTime ? ` | Last: ${new Date(data.lastRenderTime).toLocaleTimeString()}` : ''}
                        </div>
                    </div>
                `;
            });

            chartList.innerHTML = html;
        }

        function updateSidebarProtection() {
            const sidebar = document.querySelector('.sidebar');
            const isProtected = sidebar && sidebar.dataset.loadingProtection === 'true';
            
            const protectionElement = document.getElementById('sidebar-protection');
            const indicator = protectionElement.querySelector('.status-indicator');
            
            if (isProtected) {
                indicator.className = 'status-indicator active';
                protectionElement.innerHTML = '<span class="status-indicator active"></span>Active';
            } else {
                indicator.className = 'status-indicator inactive';
                protectionElement.innerHTML = '<span class="status-indicator inactive"></span>Inactive';
            }
        }

        function clearStates() {
            if (window.ViewportLazyLoader) {
                const removed = window.ViewportLazyLoader.cleanupOldStates(60000); // 1 minute
                alert(`Cleaned up ${removed.length} old chart states`);
                refreshMetrics();
            }
        }

        function toggleAutoRefresh() {
            isAutoRefresh = !isAutoRefresh;
            const button = document.getElementById('auto-refresh-text');
            
            if (isAutoRefresh) {
                autoRefreshInterval = setInterval(refreshMetrics, 2000); // Refresh every 2 seconds
                button.textContent = 'Disable Auto-Refresh';
            } else {
                if (autoRefreshInterval) {
                    clearInterval(autoRefreshInterval);
                    autoRefreshInterval = null;
                }
                button.textContent = 'Enable Auto-Refresh';
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            refreshMetrics();
            
            // Check if we're in the main dashboard
            if (window.location.pathname.includes('dashboard') || window.location.pathname === '/') {
                // Auto-enable refresh for dashboard
                setTimeout(() => {
                    if (window.ViewportLazyLoader) {
                        toggleAutoRefresh();
                    }
                }, 1000);
            }
        });

        // Cleanup on page unload
        window.addEventListener('beforeunload', function() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
            }
        });
    </script>
</body>
</html>
