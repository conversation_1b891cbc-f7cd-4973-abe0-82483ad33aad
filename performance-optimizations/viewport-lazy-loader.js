/**
 * Viewport-Based Lazy Loading System
 * Only loads chart data when components enter the viewport
 */

class ViewportLazyLoader {
  constructor() {
    this.observers = new Map();
    this.loadedComponents = new Set();
    this.loadingComponents = new Set();
    this.intersectionObserver = null;
    this.exitObserver = null; // For detecting when charts leave viewport
    this.chartStates = new Map(); // Store chart states for re-rendering
    this.destroyedCharts = new Set(); // Track destroyed charts for re-entry
    this.performanceMetrics = new Map(); // Track performance metrics
    this.initializeIntersectionObserver();
    this.initializeExitObserver();
  }

  /**
   * Initialize Intersection Observer for viewport detection
   */
  initializeIntersectionObserver() {
    const options = {
      root: null, // Use viewport as root
      rootMargin: '200px', // Enhanced buffer: Start loading 200px before entering viewport
      threshold: 0.1 // Trigger when 10% of element is visible
    };

    this.intersectionObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          this.handleElementIntersection(entry.target);
        }
      });
    }, options);

    console.log('👁️ Viewport Lazy Loader initialized with enhanced buffer zones');
  }

  /**
   * Initialize Exit Observer for detecting when charts leave viewport
   */
  initializeExitObserver() {
    const exitOptions = {
      root: null,
      rootMargin: '-100px', // Destroy charts 100px after they leave viewport (hysteresis)
      threshold: 0
    };

    this.exitObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (!entry.isIntersecting) {
          this.handleElementExit(entry.target);
        }
      });
    }, exitOptions);

    console.log('👁️ Chart Exit Observer initialized');
  }

  /**
   * Register a component for lazy loading with dynamic rendering support
   */
  registerComponent(element, config) {
    const componentId = config.id || this.generateComponentId(element);

    // Enhanced component configuration for dynamic rendering
    this.observers.set(componentId, {
      element,
      config,
      loaded: false,
      loading: false,
      destroyed: false,
      renderCount: 0,
      lastRenderTime: null,
      isDynamicChart: config.type === 'chart' && config.dynamicRendering !== false
    });

    // Add loading placeholder
    this.addLoadingPlaceholder(element, config);

    // Start observing the element for entry
    this.intersectionObserver.observe(element);

    // If it's a dynamic chart, also observe for exit
    if (config.type === 'chart' && config.dynamicRendering !== false) {
      this.exitObserver.observe(element);
      console.log(`📝 Registered dynamic chart: ${componentId}`);
    } else {
      console.log(`📝 Registered static component: ${componentId}`);
    }

    return componentId;
  }

  /**
   * Handle element entering viewport
   */
  async handleElementIntersection(element) {
    const componentData = this.findComponentByElement(element);
    if (!componentData || componentData.loaded || componentData.loading) {
      return;
    }

    const { config, element: targetElement } = componentData;
    const componentId = config.id;

    console.log(`🔄 Loading component in viewport: ${componentId}`);

    // Mark as loading
    componentData.loading = true;
    this.loadingComponents.add(componentId);

    // Protect sidebar state during chart loading
    this.protectSidebarDuringLoading(true);

    try {
      // Show loading state
      this.showLoadingState(targetElement, config);

      // Load the component data with performance monitoring
      const loadStartTime = performance.now();
      await this.loadComponentData(targetElement, config);
      const loadEndTime = performance.now();

      console.log(`📊 Component ${componentId} loaded in ${(loadEndTime - loadStartTime).toFixed(2)}ms`);

      // Mark as loaded
      componentData.loaded = true;
      this.loadedComponents.add(componentId);
      this.loadingComponents.delete(componentId);

      // Stop observing this element
      this.intersectionObserver.unobserve(targetElement);

      console.log(`✅ Component loaded: ${componentId}`);

    } catch (error) {
      console.error(`❌ Failed to load component ${componentId}:`, error);
      componentData.loading = false;
      this.loadingComponents.delete(componentId);
      this.showErrorState(targetElement, config, error);
    } finally {
      // Always release sidebar protection when loading completes (success or failure)
      this.protectSidebarDuringLoading(false);
    }
  }

  /**
   * Handle element exiting viewport (for dynamic chart destruction)
   */
  async handleElementExit(element) {
    const componentData = this.findComponentByElement(element);
    if (!componentData || !componentData.isDynamicChart || !componentData.loaded || componentData.destroyed) {
      return;
    }

    const { config, element: targetElement } = componentData;
    const componentId = config.id;

    console.log(`🗑️ Chart exiting viewport: ${componentId}`);

    // Save chart state before destruction
    this.saveChartState(targetElement, componentId);

    // Destroy the chart to free up resources
    await this.destroyChart(targetElement, componentId);

    // Mark as destroyed but keep in observers for re-entry
    componentData.destroyed = true;
    componentData.loaded = false;
    this.destroyedCharts.add(componentId);

    console.log(`✅ Chart destroyed: ${componentId}`);
  }

  /**
   * Load component data based on type with enhanced dynamic rendering support
   */
  async loadComponentData(element, config) {
    const componentId = config.id;
    const componentData = this.findComponentByElement(element);

    // Check if this is a re-entry of a destroyed chart
    if (componentData && componentData.destroyed && this.destroyedCharts.has(componentId)) {
      console.log(`🔄 Re-rendering destroyed chart: ${componentId}`);
      componentData.destroyed = false;
      this.destroyedCharts.delete(componentId);

      // Restore chart state if available
      await this.restoreChartState(element, componentId);
    }

    switch (config.type) {
      case 'chart':
        await this.loadChartComponent(element, config);
        break;
      case 'sales-card':
        await this.loadSalesCardComponent(element, config);
        break;
      case 'listing-section':
        await this.loadListingSection(element, config);
        break;
      default:
        throw new Error(`Unknown component type: ${config.type}`);
    }

    // Update performance metrics
    if (componentData) {
      componentData.renderCount++;
      componentData.lastRenderTime = Date.now();
    }
  }

  /**
   * Load chart component data
   */
  async loadChartComponent(element, config) {
    const { chartType, dataGenerator, initFunction } = config;

    // Generate chart data
    let chartData;
    if (dataGenerator) {
      chartData = await dataGenerator();
    }

    // Initialize the chart
    if (initFunction) {
      await initFunction(element, chartData);
    }

    // Remove loading placeholder
    this.removeLoadingPlaceholder(element);
  }

  /**
   * Load sales card component
   */
  async loadSalesCardComponent(element, config) {
    const { dataGenerator, renderer } = config;

    // Generate sales data
    const salesData = await dataGenerator();

    // Render the sales card
    if (renderer) {
      await renderer(element, salesData);
    }

    this.removeLoadingPlaceholder(element);
  }

  /**
   * Load listing section
   */
  async loadListingSection(element, config) {
    const { dataGenerator, renderer, virtualScroll = false } = config;

    // Generate listing data
    const listingData = await dataGenerator();

    // Use virtual scrolling for large datasets
    if (virtualScroll && listingData.length > 50) {
      this.setupVirtualScrolling(element, listingData, config);
    } else {
      // Regular rendering
      if (renderer) {
        await renderer(element, listingData);
      }
    }

    this.removeLoadingPlaceholder(element);
  }

  /**
   * Add loading placeholder
   */
  addLoadingPlaceholder(element, config) {
    const placeholder = document.createElement('div');
    placeholder.className = 'lazy-loading-placeholder';
    placeholder.innerHTML = `
      <div class="lazy-loading-content">
        <div class="lazy-loading-spinner"></div>
        <div class="lazy-loading-text">${config.loadingText || 'Loading...'}</div>
      </div>
    `;

    // Hide original content
    const originalContent = element.innerHTML;
    element.setAttribute('data-original-content', originalContent);
    element.innerHTML = '';
    element.appendChild(placeholder);
  }

  /**
   * Show loading state during data loading
   */
  showLoadingState(element, config) {
    if (window.SnapLoader) {
      window.SnapLoader.showOverlay(element, {
        text: config.loadingText || 'Loading data...',
        id: `lazy-${config.id}`
      });
    }
  }

  /**
   * Remove loading placeholder
   */
  removeLoadingPlaceholder(element) {
    const placeholder = element.querySelector('.lazy-loading-placeholder');
    if (placeholder) {
      placeholder.remove();
    }

    if (window.SnapLoader) {
      window.SnapLoader.hideOverlay(element);
    }
  }

  /**
   * Show error state
   */
  showErrorState(element, config, error) {
    const errorPlaceholder = document.createElement('div');
    errorPlaceholder.className = 'lazy-loading-error';
    errorPlaceholder.innerHTML = `
      <div class="lazy-loading-error-content">
        <div class="lazy-loading-error-icon">⚠️</div>
        <div class="lazy-loading-error-text">Failed to load ${config.type}</div>
        <button class="lazy-loading-retry-btn" onclick="window.ViewportLazyLoader.retryComponent('${config.id}')">
          Retry
        </button>
      </div>
    `;

    element.innerHTML = '';
    element.appendChild(errorPlaceholder);
  }

  /**
   * Setup virtual scrolling for large datasets
   */
  setupVirtualScrolling(element, data, config) {
    const itemHeight = config.itemHeight || 60;
    const containerHeight = element.clientHeight || 400;
    
    return window.DOMOptimizer.createVirtualScrollList(
      element,
      data,
      itemHeight,
      config.itemRenderer
    );
  }

  /**
   * Retry loading a failed component
   */
  async retryComponent(componentId) {
    const componentData = this.observers.get(componentId);
    if (componentData) {
      componentData.loaded = false;
      componentData.loading = false;
      await this.handleElementIntersection(componentData.element);
    }
  }

  /**
   * Save chart state before destruction
   */
  saveChartState(element, componentId) {
    try {
      const chart = element.snapChart;
      if (!chart) return;

      const state = {
        data: chart.data ? JSON.parse(JSON.stringify(chart.data)) : null,
        originalData: chart.originalData ? JSON.parse(JSON.stringify(chart.originalData)) : null,
        options: chart.options ? JSON.parse(JSON.stringify(chart.options)) : null,
        type: chart.type,
        currentPeriodType: chart.currentPeriodType,
        currentPeriodDuration: chart.currentPeriodDuration,
        currentPeriodId: chart.currentPeriodId,
        // Save filter states
        activeFilters: this.extractActiveFilters(element),
        // Save scroll position for scrollable charts
        scrollPosition: this.extractScrollPosition(element),
        // Save any show/hide states
        showHideStates: this.extractShowHideStates(element),
        timestamp: Date.now()
      };

      this.chartStates.set(componentId, state);
      console.log(`💾 Saved state for chart: ${componentId}`);
    } catch (error) {
      console.warn(`Failed to save state for chart ${componentId}:`, error);
    }
  }

  /**
   * Restore chart state after re-rendering
   */
  async restoreChartState(element, componentId) {
    try {
      const savedState = this.chartStates.get(componentId);
      if (!savedState) return;

      console.log(`🔄 Restoring state for chart: ${componentId}`);

      // Wait for chart to be fully rendered before restoring state
      await new Promise(resolve => setTimeout(resolve, 100));

      const chart = element.snapChart;
      if (!chart) return;

      // Restore data and options
      if (savedState.data) {
        chart.data = savedState.data;
      }
      if (savedState.originalData) {
        chart.originalData = savedState.originalData;
      }
      if (savedState.options) {
        Object.assign(chart.options, savedState.options);
      }

      // Restore period tracking
      chart.currentPeriodType = savedState.currentPeriodType;
      chart.currentPeriodDuration = savedState.currentPeriodDuration;
      chart.currentPeriodId = savedState.currentPeriodId;

      // Restore filter states
      this.restoreActiveFilters(element, savedState.activeFilters);

      // Restore scroll position
      this.restoreScrollPosition(element, savedState.scrollPosition);

      // Restore show/hide states
      this.restoreShowHideStates(element, savedState.showHideStates);

      // Re-render chart with restored state
      chart.render();

      console.log(`✅ State restored for chart: ${componentId}`);
    } catch (error) {
      console.warn(`Failed to restore state for chart ${componentId}:`, error);
    }
  }

  /**
   * Destroy chart and clean up resources
   */
  async destroyChart(element, componentId) {
    try {
      const chart = element.snapChart;
      if (!chart) return;

      // Clean up chart-specific resources
      if (typeof chart.cleanup === 'function') {
        chart.cleanup();
      }

      // Clean up Today vs Previous Years specific resources
      if (chart.cleanupTodayVsPreviousYearsChart) {
        chart.cleanupTodayVsPreviousYearsChart();
      }

      // Clean up scrollbar resources
      if (chart.cleanupScrollbar) {
        chart.cleanupScrollbar();
      }

      // Clear chart content
      if (chart.svg) {
        chart.svg.innerHTML = '';
      }
      if (chart.canvas) {
        chart.canvas.innerHTML = '';
      }

      // Remove chart reference
      delete element.snapChart;

      // Add destroyed placeholder
      this.addDestroyedPlaceholder(element, componentId);

      console.log(`🗑️ Chart destroyed and cleaned up: ${componentId}`);
    } catch (error) {
      console.warn(`Failed to destroy chart ${componentId}:`, error);
    }
  }

  /**
   * Protect sidebar state during heavy loading operations
   */
  protectSidebarDuringLoading(enable) {
    const sidebar = document.querySelector('.sidebar');
    if (!sidebar) return;

    if (enable) {
      // Add protection flag to prevent sidebar state changes during loading
      sidebar.dataset.loadingProtection = 'true';
      console.log('🛡️ Sidebar protection enabled during chart loading');
    } else {
      // Only remove protection if no other components are loading
      if (this.loadingComponents.size === 0) {
        delete sidebar.dataset.loadingProtection;
        console.log('🛡️ Sidebar protection disabled - all loading complete');
      }
    }
  }

  /**
   * Extract active filter states from chart element
   */
  extractActiveFilters(element) {
    const filters = {};

    // Extract tab states
    const activeTabs = element.querySelectorAll('.snap-chart-filter-tab.active');
    activeTabs.forEach(tab => {
      filters.activeTab = tab.getAttribute('data-filter');
    });

    // Extract show/hide button states
    const showHideButtons = element.querySelectorAll('.show-hide-dropdown-item');
    showHideButtons.forEach(button => {
      const isVisible = !button.classList.contains('hidden');
      const filterType = button.getAttribute('data-filter');
      if (filterType) {
        filters[filterType] = isVisible;
      }
    });

    return filters;
  }

  /**
   * Extract scroll position from scrollable charts
   */
  extractScrollPosition(element) {
    const scrollableContainer = element.querySelector('.snap-chart-scrollable-container');
    if (scrollableContainer) {
      return {
        scrollLeft: scrollableContainer.scrollLeft,
        scrollTop: scrollableContainer.scrollTop
      };
    }
    return null;
  }

  /**
   * Extract show/hide states
   */
  extractShowHideStates(element) {
    const states = {};
    const showHideButton = element.querySelector('.show-hide-options-btn');
    if (showHideButton) {
      states.isOpen = showHideButton.classList.contains('active');
    }
    return states;
  }

  /**
   * Restore active filter states
   */
  restoreActiveFilters(element, filters) {
    if (!filters) return;

    // Restore tab states
    if (filters.activeTab) {
      const tabs = element.querySelectorAll('.snap-chart-filter-tab');
      tabs.forEach(tab => {
        tab.classList.remove('active');
        if (tab.getAttribute('data-filter') === filters.activeTab) {
          tab.classList.add('active');
        }
      });
    }

    // Restore show/hide button states
    Object.keys(filters).forEach(filterType => {
      if (filterType !== 'activeTab') {
        const button = element.querySelector(`[data-filter="${filterType}"]`);
        if (button) {
          if (filters[filterType]) {
            button.classList.remove('hidden');
          } else {
            button.classList.add('hidden');
          }
        }
      }
    });
  }

  /**
   * Restore scroll position
   */
  restoreScrollPosition(element, scrollPosition) {
    if (!scrollPosition) return;

    const scrollableContainer = element.querySelector('.snap-chart-scrollable-container');
    if (scrollableContainer) {
      scrollableContainer.scrollLeft = scrollPosition.scrollLeft;
      scrollableContainer.scrollTop = scrollPosition.scrollTop;
    }
  }

  /**
   * Restore show/hide states
   */
  restoreShowHideStates(element, states) {
    if (!states) return;

    const showHideButton = element.querySelector('.show-hide-options-btn');
    if (showHideButton && states.isOpen) {
      showHideButton.classList.add('active');
    }
  }

  /**
   * Add destroyed placeholder to indicate chart was destroyed
   */
  addDestroyedPlaceholder(element, componentId) {
    element.innerHTML = `
      <div class="chart-destroyed-placeholder">
        <div class="destroyed-icon">📊</div>
        <div class="destroyed-text">Chart temporarily unloaded</div>
        <div class="destroyed-subtext">Will reload when scrolled into view</div>
      </div>
    `;
  }

  /**
   * Helper methods
   */
  generateComponentId(element) {
    return `lazy-${element.id || element.className.replace(/\s+/g, '-')}-${Date.now()}`;
  }

  findComponentByElement(element) {
    for (const [id, data] of this.observers) {
      if (data.element === element) {
        return data;
      }
    }
    return null;
  }

  /**
   * Get performance metrics for monitoring
   */
  getPerformanceMetrics() {
    const metrics = {
      totalComponents: this.observers.size,
      loadedComponents: this.loadedComponents.size,
      loadingComponents: this.loadingComponents.size,
      destroyedCharts: this.destroyedCharts.size,
      savedStates: this.chartStates.size,
      memoryUsage: this.estimateMemoryUsage(),
      renderCounts: this.getRenderCounts()
    };

    return metrics;
  }

  /**
   * Estimate memory usage of stored states
   */
  estimateMemoryUsage() {
    let totalSize = 0;

    this.chartStates.forEach((state, id) => {
      try {
        const stateString = JSON.stringify(state);
        totalSize += stateString.length * 2; // Rough estimate (UTF-16)
      } catch (error) {
        console.warn(`Failed to estimate size for state ${id}:`, error);
      }
    });

    return {
      estimatedBytes: totalSize,
      estimatedKB: Math.round(totalSize / 1024),
      estimatedMB: Math.round(totalSize / (1024 * 1024))
    };
  }

  /**
   * Get render counts for all components
   */
  getRenderCounts() {
    const counts = {};

    this.observers.forEach((data, id) => {
      counts[id] = {
        renderCount: data.renderCount || 0,
        lastRenderTime: data.lastRenderTime,
        isDynamic: data.isDynamicChart
      };
    });

    return counts;
  }

  /**
   * Clean up old chart states to prevent memory leaks
   */
  cleanupOldStates(maxAge = 300000) { // 5 minutes default
    const now = Date.now();
    const removedStates = [];

    this.chartStates.forEach((state, id) => {
      if (now - state.timestamp > maxAge) {
        this.chartStates.delete(id);
        removedStates.push(id);
      }
    });

    if (removedStates.length > 0) {
      console.log(`🧹 Cleaned up ${removedStates.length} old chart states`);
    }

    return removedStates;
  }

  /**
   * Get loading statistics (legacy method for compatibility)
   */
  getStats() {
    return {
      registered: this.observers.size,
      loaded: this.loadedComponents.size,
      loading: this.loadingComponents.size,
      pending: this.observers.size - this.loadedComponents.size - this.loadingComponents.size
    };
  }

  /**
   * Cleanup all observers and resources
   */
  cleanup() {
    if (this.intersectionObserver) {
      this.intersectionObserver.disconnect();
    }

    if (this.exitObserver) {
      this.exitObserver.disconnect();
    }

    this.observers.clear();
    this.loadedComponents.clear();
    this.loadingComponents.clear();
    this.chartStates.clear();
    this.destroyedCharts.clear();
    this.performanceMetrics.clear();

    console.log('🧹 Enhanced Viewport Lazy Loader cleaned up');
  }
}

// Global instance
window.ViewportLazyLoader = new ViewportLazyLoader();

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
  window.ViewportLazyLoader.cleanup();
});

/**
 * Dashboard-specific lazy loading configurations
 */
window.DashboardLazyConfigs = {
  // Today vs Previous Years Chart
  todayVsPreviousYears: {
    id: 'today-vs-previous-years-chart',
    type: 'chart',
    chartType: 'todayVsPreviousYears',
    loadingText: 'Loading 26 years of data...',
    dataGenerator: async () => {
      return await window.generateTodayVsPreviousYearsDataCached();
    },
    initFunction: async (element, data) => {
      // Initialize chart with data
      const chart = new SnapChart(element, {
        type: 'stackedColumn',
        data: data,
        // ... other chart options
      });
      element.snapChart = chart;
    }
  },

  // Monthly Sales Chart
  monthlySales: {
    id: 'monthly-sales-chart',
    type: 'chart',
    chartType: 'monthlySales',
    loadingText: 'Loading monthly data...',
    dataGenerator: async () => {
      const currentYear = new Date().getFullYear();
      return await window.generateMonthlySalesDataForYearCached(currentYear);
    },
    initFunction: async (element, data) => {
      const chart = new SnapChart(element, {
        type: 'stackedColumn',
        data: data,
        // ... other chart options
      });
      element.snapChart = chart;
    }
  },

  // Last Week Sales Chart
  lastWeekSales: {
    id: 'last-week-sales-chart',
    type: 'chart',
    chartType: 'lastWeekSales',
    loadingText: 'Loading last week data...',
    dataGenerator: async () => {
      const dataLoader = new SnapDataLoader();
      return await dataLoader.generateStackedColumnData({ days: 7 });
    },
    initFunction: async (element, data) => {
      const chart = new SnapChart(element, {
        type: 'stackedColumn',
        data: data,
        // ... other chart options
      });
      element.snapChart = chart;
    }
  },

  // Sales Cards
  salesCard: {
    id: 'sales-card',
    type: 'sales-card',
    loadingText: 'Loading sales data...',
    dataGenerator: async () => {
      return await generateFourSalesCardsMockData();
    },
    renderer: async (element, data) => {
      // Render sales card with data
      applyFourSalesCardsMockData(data);
    }
  },

  // Large Listing Sections
  listingSection: {
    id: 'listing-section',
    type: 'listing-section',
    loadingText: 'Loading listings...',
    virtualScroll: true,
    itemHeight: 120,
    dataGenerator: async () => {
      // Generate or fetch listing data
      return await generateListingData();
    },
    itemRenderer: (listing, index) => {
      // Render individual listing item
      const listingElement = document.createElement('div');
      listingElement.className = 'listing-analytics-div';
      listingElement.innerHTML = `
        <div class="listing-title">${listing.title}</div>
        <div class="listing-sales">${listing.sales}</div>
        <!-- ... other listing content -->
      `;
      return listingElement;
    }
  }
};
